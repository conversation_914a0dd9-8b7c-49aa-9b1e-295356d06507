const childProcess = require('child_process');

const semver = require('semver');
const fs = require('fs-extra');

const npmName = '@baidu/wechat-mini-app';
const registry = 'http://registry.npm.baidu-int.com';

const {ENV_BUILD, apiHost, cnap_env} = process.env;
// const ENV_BUILD = 'dev';

// 封装的 exec promise
const execAsync = async command => {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, {maxBuffer: 1024 * 1024 * 10}, (error, stdout, stderr) => {
            if (error) {
                console.error(`执行命令失败: ${command}`);
                console.error(`错误信息: ${error}`);
                reject(error || stderr);
            }
            if (stderr) {
                console.warn(`警告信息: ${stderr}`);
            }
            resolve(stdout);
        });
    });
};

// 获取当前最大版本号
const getMaxVersion = async () => {
    try {
        const versions = await execAsync(`npm view ${npmName} versions --registry=${registry}`);
        const versionList = eval(versions);
        console.log('versionList', versionList);
        return semver.maxSatisfying(versionList, '<111.111.111');
    } catch (error) {
        console.error('获取版本号失败:', error);
        throw error;
    }
};

// 生成新版本号
const generateNewVersion = async maxVersion => {
    if (ENV_BUILD === 'prod') {
        return semver.inc(maxVersion, 'patch');
    } else {
        const versions = await execAsync(`npm view ${npmName} versions --registry=${registry}`);
        const versionList = eval(versions);
        const versionIndex = versionList
            .reverse()
            .findIndex(
                item =>
                    item &&
                    typeof item === 'string' &&
                    item.includes(maxVersion) &&
                    item.includes(String(ENV_BUILD))
            );

        if (versionIndex > -1) {
            return semver.inc(versionList[versionIndex], 'prerelease', ENV_BUILD);
        } else {
            return `${maxVersion}-${ENV_BUILD}.0`;
        }
    }
};

// 更新 package.json
const updatePackageJson = async version => {
    try {
        const root = process.cwd();
        const jsonPath = `${root}/apps/wechat-app/package.json`;
        const packageJson = await fs.readJson(jsonPath);

        packageJson.version = version;
        packageJson.name = npmName;

        await fs.writeJson(jsonPath, packageJson, {spaces: 2});

        console.log(`成功更新版本号为: ${version}`);
    } catch (error) {
        console.error('更新 package.json 失败:', error);
        throw error;
    }
};

// 处理环境
const updateEnv = async () => {
    const root = process.cwd();
    const envPath = `${root}/apps/wechat-app/.env.production`;
    let envContent = await fs.readFile(envPath, 'utf8');

    if (apiHost) {
        // 更新 API_HOST
        envContent = envContent.replace(/TARO_APP_API_HOST=.*/, `TARO_APP_API_HOST=${apiHost}`);
    }

    if (cnap_env) {
        // 更新 HEADER_BAGGAGE
        envContent = envContent.replace(
            /TARO_APP_HEADER_BAGGAGE=.*/,
            `TARO_APP_HEADER_BAGGAGE=${cnap_env}`
        );
    }

    await fs.writeFile(envPath, envContent, 'utf8');
};

// 发布流程
const publishNpm = async () => {
    console.log('======= 开始构建发布流程 ==========');
    console.log(`包类型: ${ENV_BUILD}`);

    try {
        // 检查 Node.js 版本
        const nodeVersion = await execAsync('node -v');
        console.log(`当前 Node.js 版本: ${nodeVersion}`);

        // 安装依赖
        console.log('正在安装依赖...');
        await execAsync(`pnpm install --registry=${registry}`);

        // 更新环境变量
        console.log('正在更新环境变量...');
        await updateEnv();

        // 获取并生成版本号
        const maxVersion = await getMaxVersion();
        console.log(`当前最大版本: ${maxVersion}`);

        const newVersion = await generateNewVersion(maxVersion);
        console.log(`新版本号: ${newVersion}`);

        // 更新 package.json
        await updatePackageJson(newVersion);

        // 构建
        console.log('开始构建...');
        await execAsync('pnpm mini:build:weapp:pkg');

        // 发布
        console.log('开始发布...');
        // 切换到wechat-app目录
        console.log('切换到wechat-app目录...');
        process.chdir('apps/wechat-app');
        await execAsync(`npm publish --registry=${registry}`);
        console.log(`发布成功，${npmName} 版本号为：${newVersion}`);

        // 发送通知
        const ipipeLink =
            'https://console.cloud.baidu-int.com/devops/ipipe/workspaces/437573/pipelines/2380400/builds/list?branchName=branches';
        await execAsync(`sh ../../scripts/notice.sh ${npmName} ${newVersion} ${ipipeLink}`);

        console.log('======= 构建发布流程完成 ==========');
    } catch (error) {
        console.error('构建发布流程失败:', error);
        process.exit(1);
    }
};

publishNpm();
