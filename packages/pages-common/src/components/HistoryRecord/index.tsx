import {pxTransform, showLoading, hideLoading} from '@tarojs/taro';
import {memo, useCallback, useMemo, useState, useEffect} from 'react';
import cx from 'classnames';
import {View, ScrollView} from '@tarojs/components';
import {Button, SafeArea, Empty, Skeleton} from '@baidu/wz-taro-tools-core';
import {WiseDelete} from '@baidu/wz-taro-tools-icons';
import {debounce} from 'lodash-es';
import {useCreateSessionhook} from '../../../../pages-im/src/hooks/triageStream/session';
import {ubcCommonViewSend} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {getTalkList} from '../../../../pages-im/src/models/services/historyRecord/getTalkList';
import {useGetSessionId} from '../../../../pages-im/src/hooks/triageStream/pageDataController';
import {deleteTalk} from '../../../../pages-im/src/models/services/historyRecord/delTalkList';
import type {TalkItem} from '../../../../pages-im/src/models/services/historyRecord/getTalkList/index.d';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {formatTimeText} from './utils';
import styles from './index.module.less';

// 通用按钮样式类
const commonBtnClass = 'wz-flex wz-col-center';

// 分页数据size
const PAGE_SIZE = 15;

// 节流时间
const DEBOUNCETIME = 1000;

const SkeletonItem = () => (
    <View className={cx(styles.skeletonItem, 'wz-flex wz-col-center wz-ptb-51 wz-plr-51')}>
        <View className={cx(styles.historyItem, 'wz-pr-45')}>
            <Skeleton className={cx(styles.skeletonText)} animation='wave' />
            <Skeleton className={cx(styles.skeletonTime)} animation='wave' />
        </View>
        <View className={cx(styles.operate, 'wz-flex')}>
            <Skeleton className={cx(styles.skeletonDelete)} animation='wave' />
        </View>
    </View>
);

const SkeletonList = ({list}: {list: number}) => (
    <>
        {Array(list)
            .fill(null)
            .map((_, index) => (
                <SkeletonItem key={index} />
            ))}
    </>
);

const HistoryRecord = () => {
    const curSessionId = useGetSessionId();
    const {createSession} = useCreateSessionhook();
    const [activeOperateBtnId, setActiveOperateBtnId] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [hasTalkListLoaded, setHasTalkListLoaded] = useState(false);
    const [talkList, setTalkList] = useState<TalkItem[]>([]);
    const [lastSessionId, setLastSessionId] = useState<string | undefined>(undefined);

    // 获取对话列表
    const fetchTalkList = useCallback(
        async (viewType: 'next' | 'pre', sessionId?: string, isInit = false) => {
            setIsLoading(true);
            try {
                const params = {
                    viewType,
                    size: PAGE_SIZE,
                    sessionId: sessionId || undefined
                };
                const [, res] = await getTalkList(params);
                setIsLoading(false);
                setHasTalkListLoaded(true);
                const list = res?.data?.list || [];
                setHasMore(res?.data?.hasMore ?? false);
                if (isInit) {
                    setTalkList(list);
                } else {
                    setTalkList(prev => [...prev, ...list]);
                }
                if (list.length > 0) {
                    setLastSessionId(list[list.length - 1].sessionId);
                }
            } catch (error) {
                setHasMore(false);
            } finally {
                setIsLoading(false);
            }
        },
        []
    );

    // 初始化接口数据
    useEffect(() => {
        fetchTalkList('next', undefined, true);
        ubcCommonViewSend({
            value: 'historyRecordList',
            ext: {
                product_info: {
                    sessionId: curSessionId || ''
                }
            }
        });
    }, [fetchTalkList, curSessionId]);

    /**
     * 处理加载更多
     */
    const handleLoadMore = useCallback(async () => {
        if (isLoading || !hasMore) return;
        fetchTalkList('next', lastSessionId);
    }, [isLoading, hasMore, lastSessionId, fetchTalkList]);

    /**
     * 删除TalkItem 数据
     */
    const handleDelTalkItem = useCallback(async () => {
        if (!activeOperateBtnId) return;
        showLoading({
            title: '正在删除...',
            mask: true
        });
        try {
            await deleteTalk({sessionId: activeOperateBtnId});
            setTalkList(prev => prev.filter(item => item.sessionId !== activeOperateBtnId));

            // 如果删除会话是当前聊天会话则新建会话
            if (activeOperateBtnId === curSessionId) {
                createSession();
            }
            setActiveOperateBtnId('');

            hideLoading();

            // 如果当前页数据不足，且不是最后一页，加载更多数据
            if (talkList.length <= PAGE_SIZE && hasMore) {
                handleLoadMore();
            }
        } catch (error) {
            console.error(error);
        }
    }, [activeOperateBtnId, curSessionId, talkList.length, hasMore, createSession, handleLoadMore]);

    /**
     * 切换会话 数据
     */
    const handleSwitchSession = useCallback(talk => {
        const {sessionId} = talk;
        navigate({
            url: `vita/pages/im/index?sessionId=${sessionId}&tab=ai&refreshSymbol=historyRecordPage`,
            openType: 'relaunch'
        });
    }, []);

    /**
     * 处理滚动到底部
     */
    const handleScrollToLower = useCallback(() => {
        handleLoadMore();
    }, [handleLoadMore]);

    const renderEmpty = useMemo(() => {
        return (
            <Empty className={cx(styles.historyRecordEmpty, 'wz-flex')}>
                <Empty.Image
                    className={styles.emptyImg}
                    src='https://med-fe.cdn.bcebos.com/vita/defaultDiagram.png'
                />
                <Empty.Description>暂无历史记录</Empty.Description>
            </Empty>
        );
    }, []);

    /**
     * 渲染操作区域（删除按钮）
     * @param sessionId - 对话组ID
     * @returns 操作按钮组件
     */
    const renderDelContent = useCallback(
        (sessionId: string) => {
            // 当前对话组显示取消和删除按钮
            if (sessionId === activeOperateBtnId) {
                return (
                    <View className={cx(styles.operate, 'wz-flex')}>
                        <Button
                            size='small'
                            className={cx(styles.cancelBtn, commonBtnClass)}
                            style={{
                                backgroundColor: '#F5F6FA',
                                marginRight: pxTransform(24)
                            }}
                            onClick={() => {
                                setActiveOperateBtnId('');
                            }}
                        >
                            取消
                        </Button>
                        <Button
                            size='small'
                            color='danger'
                            className={cx(styles.delBtn, commonBtnClass)}
                            onClick={debounce(() => handleDelTalkItem(), DEBOUNCETIME, {
                                leading: true,
                                trailing: false
                            })}
                        >
                            删除
                            <WiseDelete className={styles.delBtnIcon} color='#fff' size={60} />
                        </Button>
                    </View>
                );
            }

            // 其他对话组只显示删除图标
            return (
                <WiseDelete
                    size={60}
                    color='#848691'
                    onClick={() => {
                        setActiveOperateBtnId(sessionId);
                    }}
                />
            );
        },
        [activeOperateBtnId, handleDelTalkItem]
    );

    /**
     * 渲染单个对话项
     * @param talk - 对话数据
     * @returns 对话项组件
     */
    const renderTalkItem = useCallback(
        (talk: TalkItem) => {
            return (
                <>
                    <View
                        className={cx(styles.historyItem, 'wz-pr-45')}
                        onClick={() => {
                            handleSwitchSession(talk);
                        }}
                    >
                        <View className={cx(styles.historyText, 'wz-fs-48')}>
                            {talk.firstMsgText || ''}
                        </View>
                        <View className={cx(styles.historyTime, 'wz-fs-39 wz-mt-27')}>
                            {formatTimeText(talk.createTime)}
                        </View>
                    </View>
                    {renderDelContent(talk.sessionId || '')}
                </>
            );
        },
        [handleSwitchSession, renderDelContent]
    );

    /**
     * 渲染对话列表
     */
    const renderTalkList = useMemo(() => {
        if (talkList.length === 0 && !isLoading) {
            return <View className={cx(styles.historyRecordEmpty)}>{renderEmpty}</View>;
        }
        if (talkList.length === 0) return null;
        return talkList?.map(talk => {
            const isCurTalk = talk.sessionId === activeOperateBtnId;
            return (
                <View
                    className={cx(
                        styles.historyItemWrap,
                        isCurTalk ? styles.btnActive : '',
                        'wz-flex wz-col-center'
                    )}
                    key={talk?.sessionId}
                >
                    {renderTalkItem(talk)}
                </View>
            );
        });
    }, [isLoading, talkList, renderEmpty, activeOperateBtnId, renderTalkItem]);

    /**
     * 渲染加载状态
     */
    const renderLoading = useMemo(() => {
        if (!talkList?.length) {
            return null;
        }
        const text = !hasMore ? '没有更多了' : isLoading ? '加载中...' : '';

        return (
            <View className={cx('wz-flex wz-row-center wz-col-center wz-ptb-30')}>
                <View className={cx(styles.loadingText, 'wz-fs-42 wz-text-center')}>{text}</View>
            </View>
        );
    }, [talkList?.length, hasMore, isLoading]);

    // 解决苹果骨架屏偶现不消失的问题
    const memoForcerenderKey = useMemo(() => {
        if (process.env.TARO_ENV === 'swan') {
            return talkList?.length ? 'list' : 'empty';
        }
        return 'default';
    }, [talkList?.length]);

    return (
        <>
            <View className={cx(styles.historyCon, 'wz-flex')}>
                {/* 列表区域 */}
                <ScrollView
                    scrollY
                    className={styles.historyListWrap}
                    key={memoForcerenderKey}
                    onScrollToLower={handleScrollToLower}
                >
                    {!hasTalkListLoaded && <SkeletonList list={6} />}
                    {hasTalkListLoaded && (
                        <>
                            {renderTalkList}
                            {renderLoading}
                        </>
                    )}
                    <SafeArea position='bottom' className={styles.safeArea} />
                </ScrollView>
            </View>
        </>
    );
};

HistoryRecord.displayName = 'HistoryRecord';
export default memo(HistoryRecord);
