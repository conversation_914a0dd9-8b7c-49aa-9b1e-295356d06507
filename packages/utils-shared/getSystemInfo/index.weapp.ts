import {
    getMenuButtonBoundingClientRect,
    getSystemSetting,
    getAppAuthorizeSetting,
    getDeviceInfo,
    getWindowInfo,
    getAppBaseInfo
} from '@tarojs/taro';
import {SystemInfoProps} from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    const systemSetting = getSystemSetting(); // 系统设置
    const appAuthorizeSetting = getAppAuthorizeSetting(); // 授权设置
    const deviceInfo = getDeviceInfo(); // 设备信息
    const windowInfo = getWindowInfo(); // 窗口信息
    const appBaseInfo = getAppBaseInfo(); // 应用基础信息

    const sysInfo = {
        ...systemSetting,
        ...appAuthorizeSetting,
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
    };

    const menuRect = getMenuButtonBoundingClientRect();
    const {safeArea = {}, screenHeight, statusBarHeight = 0} = sysInfo;
    const navigationBarHeight = (menuRect.top - statusBarHeight) * 2 + menuRect.height;
    const bottomSafeAreaHeight = screenHeight - safeArea.bottom;

    const info = {
        ...sysInfo,
        navigationBarHeight,
        bottomSafeAreaHeight,
        barHeight: navigationBarHeight + statusBarHeight
    };

    return info;
};
