{"name": "@baidu/vita-pages-im", "version": "1.0.0", "private": true, "description": "AI 健康助手", "templateInfo": {"name": "default", "typescript": true, "css": "None", "framework": "React"}, "sideEffects": ["*.css", "*.scss", "*.less", "**/src/**/*.css", "**/src/**/*.scss"], "main": "index.tsx", "scripts": {"test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@baidu/antifraud-swan": "^1.3.0", "@baidu/antifraud-xaf3": "^3.3.53", "@baidu/boxx": "^4.0.4", "@baidu/health-utils": "1.1.3", "@baidu/vita-ui-cards-common": "workspace:*", "@baidu/vita-ui-cards-wz": "workspace:*", "@baidu/vita-utils-shared": "workspace:*", "@baidu/xaf-we": "^1.1.1", "@searchfe/user-agent": "^1.9.14", "classnames": "^2.5.1", "js-base64": "^3.7.7", "sse-kit": "1.0.9"}, "devDependencies": {"react-scan": "^0.1.3"}}