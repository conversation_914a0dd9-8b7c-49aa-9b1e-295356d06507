import {cloneDeep} from 'lodash-es';
import type {ImFlowData} from '@baidu/vita-ui-cards-common';

/**
 * @file ContentList 合并工具函数
 * @description ContentList 对象合并功能
 */

// 类型定义

type ContentList = ImFlowData['list'][number]['contentList'];

/**
 * @param originalContentList 原始 contentList 对象
 * @param appendContentList 要追加的 contentList 对象
 * @returns 合并后的 contentList 对象，如果两个参数都为空则返回 undefined
 */
export function mergeContentList(
    originalContentList?: ContentList,
    appendContentList?: ContentList
): ContentList | undefined {
    if (!originalContentList && !appendContentList) {
        return undefined;
    }

    if (!originalContentList) {
        return appendContentList;
    }

    if (!appendContentList) {
        return originalContentList;
    }

    const result: ContentList = {
        ids: [...(originalContentList.ids || [])],
        data: {...(originalContentList?.data || {})}
    };

    if (!result.data) {
        result.data = {};
    }

    const appendIds = appendContentList.ids || [];

    appendIds.forEach((id: string) => {
        const originItem = result.data[id] || {};
        const appendItem = appendContentList.data?.[id];

        // 如果 appendItem 不存在，跳过
        if (!appendItem) {
            return;
        }

        // 创建合并后的项目
        const mergedItem = {...originItem};

        // 如果 originItem.isFinish 为 true，则不在合并
        if (appendItem.contentAdd !== undefined && !originItem?.isFinish) {
            mergedItem.contentAdd = (originItem?.contentAdd || '') + appendItem?.contentAdd;
        }

        if (appendItem.contentReplace !== undefined) {
            mergedItem.contentReplace = cloneDeep(appendItem.contentReplace);
        }

        Object.keys(appendItem).forEach(key => {
            if (key !== 'contentAdd' && key !== 'contentReplace') {
                mergedItem[key] = appendItem[key];
            }
        });

        result.data[id] = mergedItem;
    });

    const mergedIds = [...result.ids];
    appendIds.forEach(id => {
        if (!mergedIds.includes(id)) {
            mergedIds.push(id);
        }
    });
    result.ids = mergedIds;

    return result;
}

type FlowSectionItem = ImFlowData['list'][number];

/**
 * 合并sse流中的Imflow的数据
 *
 * @param sourceItem 源项目
 * @param appendItem 要追加的项目
 * @param options 合并选项
 * @returns 合并后的项目
 */
export interface MergeItemOptions {
    /** 需要合并的属性列表，如果不指定则使用默认列表 */
    mergeProperties?: string[];
}

const DEFAULT_MERGE_PROPERTIES = [
    'plan',
    'markdownBtn',
    'media',
    'tabList',
    'planIcon',
    'planProgressDesc',
    'sectionShowMore',
    'sectionHeader',
    'isFinish'
];

export function mergeItemProperties(
    sourceItem: FlowSectionItem,
    appendItem: FlowSectionItem,
    options: MergeItemOptions = {}
): FlowSectionItem {
    const {mergeProperties = DEFAULT_MERGE_PROPERTIES} = options;

    const result = {...sourceItem};

    // 处理 content 属性的合并，追加逻辑
    if (appendItem.content !== undefined) {
        const originalContent = sourceItem.content || '';
        const appendContent = appendItem.content || '';
        result.content = `${originalContent}${appendContent}`;
    }

    // 处理其他属性的合并，替换逻辑
    mergeProperties.forEach(property => {
        if (appendItem[property] !== undefined) {
            result[property] = appendItem[property];
        }
    });

    return result;
}
