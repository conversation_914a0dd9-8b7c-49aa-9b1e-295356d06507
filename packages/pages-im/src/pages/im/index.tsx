import {useAtomValue} from 'jotai';
import {View, Block} from '@tarojs/components';
import {useEffect, memo, type FC} from 'react';
import {showLoading, hideLoading, eventCenter} from '@tarojs/taro';
import {CLoginWxPopup} from '@baidu/vita-ui-cards-common';

import {test} from '@baidu/vita-utils-shared/test';

import NpsPopup from '../../components/nps/NpsPopup';
import ImTopNavBar from '../../components/ImTopNavBar';
import CPageContainer from '../../components/CPageContainer';
import CAntipassPopup from '../../components/CAntipassPopup';
import AIFollowUpDialog from '../../components/AIFollowUpDialog';
import GlobalTouchListener from '../../components/GlobalTouchListener';
import TipImgUploadPopup from '../../components/pagePopup/TipImgUploadPopup';
import {createErrorBoundary} from '../../components/ErrorBoundary/ErrorBoundaryHoc';

import {isUploadImgType} from '../../constants/popup';

import {useNpsIm} from '../../hooks/useNpsIm';
import {useGetUrlParams} from '../../hooks/common';
import {useImageUpload} from '../../hooks/useImageUpload';
import {useRetentionDialog} from '../../hooks/useRetentionDialog';
import {useInitDataController} from '../../hooks/triageStream/useInitDataController';
import {useGetPopData, useGetUserData} from '../../hooks/triageStream/pageDataController';
import {useGetSwanMsgListSceneStatus} from '../../hooks/triageStream/useGetSwanMsgListSceneStatus';
import {useGetTBotEntranceCardStatus} from '../../hooks/triageStream/dataController';

import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';

import {
    resetSessionIdAtom,
    triageStreamAtomStore,
    sessionAreaLoadingAtom,
    getTitleDataAtom
} from '../../store/triageStreamAtom';
import {resetModalAtom} from '../../store/viewRenderAtom';

import OperatingArea from './components/OperatingArea';
import SessionContent from './components/SessionContent';

import styles from './index.module.less';

const h5BgColor =
    'left top / 100% auto no-repeat url("https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg.png")';
const barBg = process.env.TARO_ENV === 'h5' ? h5BgColor : 'inherit';

const TriageStreamPage: FC = () => {
    const sessionAreaLoading = useAtomValue(sessionAreaLoadingAtom);
    const {title = '', menu, titleTips = ''} = getTitleDataAtom();

    const {antiPassData, getData, releaseFirstConversation, canRender} = useInitDataController();
    const {
        showDialog,
        showNpsPop,
        npsPopData,
        backDetain,
        onBackDetain,
        closeDialog,
        initRetentionCheck,
        closeNpsPopup
    } = useRetentionDialog();
    const {userData} = useGetUserData();
    const {isShowBotEntranceCard} = useGetTBotEntranceCardStatus();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const {refreshSymbol: refreshSymbolType, ctrlType} = useGetUrlParams();

    useNpsIm();
    const {popData} = useGetPopData() || {};
    const {type, uploadImgInstruction} = popData || {};

    const {popupOpenStatus, closeUploadPopup, onSelectedPics, openUploadPopup} = useImageUpload();

    useEffect(() => {
        // eslint-disable-next-line no-console
        console.warn('支持手百消息中心版本，是否为手百消息频道场景', isSwanMsgListScene);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        // 上传图片场景入参带入展示弹层
        const isPageRefresh = refreshSymbolType === 'pageRefresh';
        const shouldShowUploadPopup =
            isUploadImgType(type) &&
            uploadImgInstruction &&
            !isPageRefresh &&
            isUploadImgType(ctrlType);

        if (shouldShowUploadPopup) {
            openUploadPopup();
        } else {
            closeUploadPopup();
        }
    }, [
        type,
        uploadImgInstruction,
        openUploadPopup,
        refreshSymbolType,
        ctrlType,
        closeUploadPopup
    ]);

    useEffect(() => {
        initRetentionCheck();
    }, [initRetentionCheck]);
    const {status} = useGetSwanMsgListSceneStatus();

    useEffect(() => {
        if (process.env.TARO_ENV === 'h5') {
            document.title = 'AI健康管家';
        }
        getData('init', {
            initType: 'default'
        });

        ubcCommonViewSend({
            value: 'page'
        });
        test();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        sessionAreaLoading &&
            showLoading({
                title: '加载中...'
            });
        !sessionAreaLoading && hideLoading();

        return () => {
            hideLoading();
        };
    }, [sessionAreaLoading]);
    return (
        <GlobalTouchListener
            onTouch={e => {
                eventCenter.trigger('im_page_touch', e);
            }}
        >
            <CPageContainer
                onBackDetain={onBackDetain}
                backDetain={backDetain}
                topBarProps={{
                    title: ' ',
                    className: 'white',
                    textColor: '#000311',
                    barBg: barBg,
                    blank: true,
                    hideHome: true,
                    titleLeftSlot: title,
                    titleTips,
                    isLogin: userData?.isLogin,
                    menu,
                    // 手百中心入口进来的隐藏返回按钮 @zhangzhiyu03
                    ...(status
                        ? {
                            hideBack: true,
                            isInTabBar: true
                        }
                        : {})
                }}
                showSkeleton={!canRender}
                skeletonName='vitaIm'
                className={'pageContainerCover'}
            >
                <View className={styles.triageStreamContainer}>
                    {/* 去掉顶部订单入口等 */}
                    <ImTopNavBar hideBottomLine={true} />
                    <SessionContent />
                    <Block>
                        <OperatingArea />
                    </Block>
                    {process.env.TARO_ENV === 'h5' && antiPassData && (
                        <CAntipassPopup
                            data={antiPassData?.data}
                            fkParams={antiPassData?.fkParams}
                            callback={releaseFirstConversation}
                            refreshParams={antiPassData?.refreshParams}
                        />
                    )}
                </View>

                {showDialog && isShowBotEntranceCard === '1' && (
                    <AIFollowUpDialog onClose={closeDialog} />
                )}
                <NpsPopup
                    open={showNpsPop}
                    triggerType={1}
                    onClosePopup={closeNpsPopup}
                    popInfo={npsPopData?.npsModel}
                />

                <TipImgUploadPopup
                    open={popupOpenStatus}
                    sceneType='uploadImg'
                    onSelectedPics={onSelectedPics}
                    closeUploadPopup={closeUploadPopup}
                    tipsData={uploadImgInstruction}
                />
                <CLoginWxPopup />
            </CPageContainer>
        </GlobalTouchListener>
    );
};

/**
 *
 * @description 用于包裹页面组件，控制渲染时序，明确局部 Provider
 * @returns 返回渲染后的 React 元素。
 */
const TriageStreamPageContainer: FC = () => {
    useEffect(() => {
        return () => {
            resetSessionIdAtom();
            resetModalAtom(triageStreamAtomStore);
            // eslint-disable-next-line no-console
            console.warn('TriageStreamPageContainer 相关 Atom 已重置');
        };
    }, []);

    return <TriageStreamPage />;
};

export default createErrorBoundary(memo(TriageStreamPageContainer));
