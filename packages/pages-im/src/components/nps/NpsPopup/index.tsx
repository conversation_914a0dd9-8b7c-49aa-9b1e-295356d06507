/**
 * @file nps用户调研弹层
 * @author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {memo, FC, useState, useCallback, useEffect} from 'react';
import {hideLoading, pxTransform, showLoading} from '@tarojs/taro';
import {ScrollView, View, Image} from '@tarojs/components';
import {Button, Popup, SafeArea, Textarea} from '@baidu/wz-taro-tools-core';

import cx from 'classnames';
import {throttle} from 'lodash-es';
import {submitNpsInfo} from '../../../models/services/nps/submitNpsInfo';

import {ubcCommonClkSend, ubcCommonViewSend} from '../../../utils/generalFunction/ubc';
import {showToast} from '../../../utils/customShowToast';
import {navigate} from '../../../utils/basicAbility/commonNavigate';
import {imgUrlMap} from '../../../constants/resourcesOnBos';
import {NPS_INPUT_MAX_LENGTH} from '../../../constants/common';
import {useGetSessionId} from '../../../hooks/triageStream/pageDataController';
import {isEmpty} from '../../../utils';
import {NpsModel, ScoreItemValue} from '../../../models/services/nps/getNpsInfo/index.d';
import type {
    BaiduQuestionnairBtnInfo,
    SubmitNpsParams
} from '../../../models/services/nps/submitNpsInfo/index.d';

import ScoreOption from '../components/ScoreOption';

import type {IProps, ConfigRation, FlatScaleConfiguration, TagsInfo} from './index.d';
import styles from './index.module.less';

const ImNpsPop: FC<IProps> = props => {
    const {
        onClosePopup,
        open,
        selectedScale,
        popInfo,
        triggerType,
        changeNpsCardStatus,
        appraiseId,
        msgId
    } = props;
    const [popupOpen, setPopupOpen] = useState(open);
    // 数据是否加载完成
    const [isDataLoaded, setIsDataLoaded] = useState(false);
    // 用户是否参与过调研
    const [isParticipated] = useState<0 | 1>(0);
    // 弹层数据
    const [popData, setPopData] = useState<NpsModel>(popInfo || ({} as NpsModel));
    // 用户分值数组
    const [scaleArr, setScaleArr] = useState<FlatScaleConfiguration[]>([]);
    // 用户点击分值
    const [userScale, setUserScale] = useState<ScoreItemValue>(selectedScale as ScoreItemValue);
    // 分段配置
    const [scaleConfiguration, setScaleConfiguration] = useState<ConfigRation>({});
    // 用户tags数组
    const [tagsArr, setTagsArr] = useState<TagsInfo[]>([]);
    // 输入框发表评价
    const [textareaVal, setTextareaVal] = useState('');
    // 提交成功之后的文案
    const [successText, setSuccessText] = useState('');
    // 百度问卷
    const [bdQuestionnairBtn, setBdQuestionnairBtn] = useState<BaiduQuestionnairBtnInfo>({});
    const curSessionId = useGetSessionId();

    // 转换弹层数据
    const handleNpsPopData = useCallback(
        (popData: NpsModel) => {
            // 用户可选分值数组
            const responseArr = popData?.scaleConfiguration
                ?.map(item => {
                    return item?.scoreShow?.map(scoreShow => ({
                        ...scoreShow,
                        configRation: item.configRation
                    }));
                })
                .flat();

            setScaleArr(responseArr || []);
            setUserScale(selectedScale || '');

            setIsDataLoaded(true);
        },
        [selectedScale]
    );

    useEffect(() => {
        if (!isEmpty(popInfo)) {
            handleNpsPopData(popInfo as NpsModel);
            setPopData(popInfo as NpsModel);
            setPopupOpen(open);
        }
    }, [handleNpsPopData, open, popData, popInfo]);

    // 用户配置分段配置
    useEffect(() => {
        if (scaleArr?.length) {
            const {configRation} = scaleArr?.find(item => item?.value === userScale) || {};
            setScaleConfiguration({...(configRation || {})});

            if (!isParticipated) {
                // tagsArr
                const tags = configRation?.tags?.map(tag => ({text: tag, isSelected: false})) || [];
                setTagsArr([...tags]);
            }
        }
    }, [userScale, scaleArr, isParticipated]);

    useEffect(() => {
        // nps弹层点击打点
        ubcCommonViewSend({
            value: `nps_pop_show_${triggerType}`,
            ext: {
                value_type: 'npsRenderType',
                value_id: popData.npsRenderType || ''
            }
        });
    }, [popData.npsRenderType, triggerType]);

    useEffect(() => {
        if (triggerType === 1 && userScale) {
            ubcCommonViewSend({
                value: `nps_pop_score_${triggerType}`
            });
        }
    }, [triggerType, userScale]);

    // tags点击
    const handleTagsClick = useCallback(
        (tag, index) => {
            if (isParticipated) {
                return;
            }

            const newTagsArr = [...tagsArr];
            newTagsArr[index].isSelected = !tag.isSelected;
            setTagsArr([...newTagsArr]);
        },
        [tagsArr, isParticipated]
    );

    // 评价内容输入
    const handleTextarea = useCallback(e => {
        setTextareaVal(e.detail.value);
    }, []);

    // 关闭弹窗
    const closePopup = useCallback(() => {
        onClosePopup?.();
        setPopupOpen(false);
    }, [onClosePopup]);

    // 打开百度问卷
    const goToBdQuestionnair = useCallback(() => {
        if (isEmpty(bdQuestionnairBtn)) {
            return;
        }
        const {interaction, interactionInfo} = bdQuestionnairBtn;
        if (interaction === 'openLink') {
            if (process.env.TARO_ENV === 'h5') {
                window.location.href = interactionInfo?.url;
                return;
            }
            if (process.env.TARO_ENV === 'swan') {
                navigate({
                    url: interactionInfo?.url,
                    openType: 'easybrowse'
                });
            }
        }
    }, [bdQuestionnairBtn]);

    // 提交评论
    const submitComment = useCallback(async () => {
        try {
            ubcCommonClkSend({
                value: `nps_pop_submit_${triggerType}`
            });
        } catch (e) {
            console.error(e);
        }

        const {isTagRequired, customInput} = scaleConfiguration || {};
        // 判断tag是否必填，以及是否选择tag
        if (isTagRequired && !tagsArr?.some(tag => tag.isSelected)) {
            showToast({
                icon: 'none',
                title: '请选择至少一个标签'
            });

            return;
        }

        // 判断评价字数
        if (customInput?.isRequired && textareaVal?.length < 1) {
            showToast({
                icon: 'none',
                title: '请输入评价内容'
            });

            return;
        }

        const maxLen = customInput?.maxLen || NPS_INPUT_MAX_LENGTH;
        if (maxLen && textareaVal?.length > maxLen) {
            showToast({
                icon: 'none',
                title: `输入评价应小于${maxLen}字`
            });

            return;
        }

        showLoading({
            title: '评价提交中，请稍候...',
            mask: true
        });

        try {
            const tagsResult =
                tagsArr?.filter(tag => tag?.isSelected)?.map(tag => tag?.text || '') || [];
            const submitData = {
                score: userScale,
                tags: tagsResult,
                input: textareaVal,
                triggerType,
                appraiseId,
                msgId: msgId || '',
                sessionId: curSessionId,
                ...(popData?.btn?.interactionInfo?.params || {})
            };
            const [err, data] = await submitNpsInfo(submitData as SubmitNpsParams);
            hideLoading();

            if (!err && data?.status === 0) {
                if (data?.data?.hasExpired) {
                    showToast({
                        icon: 'none',
                        title: data?.data?.expiredDesc || '当前调研已结束，谢谢您的参与'
                    });
                    closePopup();

                    return;
                }

                if (data?.data?.popupDesc) {
                    setSuccessText(data?.data?.popupDesc);

                    changeNpsCardStatus?.(data?.data?.cardSuccessInfo || {});
                }

                if (data?.data?.baiduQuestionnairBtnInfo) {
                    setBdQuestionnairBtn(data?.data?.baiduQuestionnairBtnInfo);
                    ubcCommonViewSend({
                        value: `nps_pop_baidu_questionnair_${triggerType}`
                    });
                }

                return;
            }

            closePopup();
            showToast({
                icon: 'none',
                title: data?.toast || err?.toast || '提交失败，请稍后重试'
            });
        } catch (error) {
            hideLoading();
            showToast({
                icon: 'none',
                title: error?.[0]?.toast || '提交失败，请稍后重试'
            });
            closePopup();
        }
    }, [
        textareaVal,
        scaleConfiguration,
        userScale,
        appraiseId,
        msgId,
        curSessionId,
        triggerType,
        popData?.btn?.interactionInfo?.params,
        changeNpsCardStatus,
        closePopup,
        tagsArr
    ]);

    // 提交评价
    const submitCommentHander = throttle(submitComment, 1000);

    // 渲染标签
    const renderTags = useCallback(() => {
        return tagsArr?.map((item, index) => {
            return (
                <View
                    className={cx(
                        'wz-mt-24 wz-flex wz-col-center wz-row-center wz-fs-42 wz-br-27',
                        styles.tag,
                        item?.isSelected ? styles.select : '',
                        index % 2 !== 0 ? 'wz-ml-21' : ''
                    )}
                    key={`tag_${index}`}
                    onClick={() => handleTagsClick(item, index)}
                >
                    {item?.text || ''}
                </View>
            );
        });
    }, [tagsArr, handleTagsClick]);

    // 获取用户调研表单
    const getNpsForm = useCallback(() => {
        if (!userScale) {
            return null;
        }

        return (
            <>
                {!!tagsArr?.length && (
                    <View className={cx('wz-flex wz-col-center wz-flex-wrap', styles.tagsWrapper)}>
                        {renderTags()}
                    </View>
                )}
                {((!isParticipated && !!scaleConfiguration?.customInput) ||
                    (isParticipated === 1 && !!textareaVal)) && (
                    <View className={cx(styles.inputWrapper, 'wz-mt-45')}>
                        <View
                            className={cx(
                                'wz-br-36 wz-pt-30 wz-plr-45',
                                styles.commentCont,
                                isParticipated ? 'wz-pb-45' : styles.textareaPlace
                            )}
                        >
                            <Textarea
                                placeholder={
                                    scaleConfiguration?.customInput?.placeholder || '请输入您的评价'
                                }
                                value={textareaVal}
                                maxlength={
                                    scaleConfiguration?.customInput?.maxLen || NPS_INPUT_MAX_LENGTH
                                }
                                cursor-spacing='105'
                                disabled={!!isParticipated}
                                style={{height: pxTransform(201)}}
                                onInput={handleTextarea}
                            />
                        </View>
                        {!isParticipated && (
                            <View className={cx(styles.commentTextLen, 'wz-fs-42')}>
                                {textareaVal?.length}/
                                {scaleConfiguration?.customInput?.maxLen || NPS_INPUT_MAX_LENGTH}
                            </View>
                        )}
                    </View>
                )}
                {/* 按钮 */}
                {!isParticipated && popData?.btn && (
                    <View className={cx('wz-ptb-24', 'wz-plr-51', styles.buttonWrapper)}>
                        <Button
                            shape='round'
                            size='large'
                            style={{border: 'none'}}
                            disabled={
                                scaleConfiguration.isTagRequired === 1 &&
                                !tagsArr?.some(tag => tag?.isSelected)
                            }
                            className={cx(styles.button, 'wz-fw-500')}
                            onClick={() => {
                                submitCommentHander();
                            }}
                        >
                            {popData?.btn?.text || '提交反馈'}
                        </Button>
                        <SafeArea position='bottom' style={{background: '#fff'}} />
                    </View>
                )}
            </>
        );
    }, [
        tagsArr,
        renderTags,
        textareaVal,
        handleTextarea,
        isParticipated,
        popData?.btn,
        userScale,
        scaleConfiguration?.customInput,
        scaleConfiguration.isTagRequired,
        submitCommentHander
    ]);

    const getCon = useCallback(() => {
        if (successText) {
            return (
                <View className={cx(styles.successWrap, 'wz-text-center')}>
                    <Image
                        src={imgUrlMap?.npsSuccessIcon}
                        className={styles.successImage}
                        mode='aspectFill'
                    />
                    <View className={cx(styles.successText, 'wz-fs-51 wz-mt-30')}>
                        {successText}
                    </View>
                    {!isEmpty(bdQuestionnairBtn) && (
                        <Button
                            size='small'
                            className={cx(styles.successBtn, 'wz-fw-500')}
                            shape='round'
                            onClick={() => {
                                // 打开百度问卷
                                goToBdQuestionnair();
                            }}
                        >
                            {bdQuestionnairBtn?.value || '去反馈'}
                        </Button>
                    )}
                </View>
            );
        }

        return (
            <ScrollView scrollY enhanced bounces={false}>
                <View className='wz-plr-51 wz-pb-30'>
                    <View
                        className={cx(
                            styles.popContent,
                            userScale || successText ? 'wz-pb-45' : ''
                        )}
                    >
                        {popData?.title && (
                            <View
                                className={cx(styles.title, 'wz-fw-500 wz-pb-54 wz-fs-51 wz-pr-81')}
                            >
                                {popData?.title || ''}
                            </View>
                        )}
                        <ScoreOption
                            npsRenderType={popData.npsRenderType}
                            userScale={userScale}
                            scaleArr={scaleArr}
                            userResponse={popData?.userResponse}
                            isParticipated={isParticipated}
                            triggerType={triggerType}
                            handleScaleClick={res => setUserScale(res)}
                        />
                        {getNpsForm()}
                    </View>
                    {/* 按钮占位符 */}
                    {!isParticipated && (userScale || successText) && popData?.btn && (
                        <View className={styles.buttonPlacement} />
                    )}
                </View>
                <SafeArea position='bottom' style={{background: '#fff'}} />
            </ScrollView>
        );
    }, [
        bdQuestionnairBtn,
        getNpsForm,
        goToBdQuestionnair,
        isParticipated,
        triggerType,
        popData,
        scaleArr,
        successText,
        userScale
    ]);

    return (
        <>
            <Popup
                placement='bottom'
                open={popupOpen && isDataLoaded}
                style={{
                    zIndex: 1005,
                    background:
                        '#fff url(https://med-fe.cdn.bcebos.com/wenzhen-mini-app/nps_pop_bg.png) no-repeat top left',
                    boxShadow: '0 -5px 20px 0 rgba(0,0,0,0.08)',
                    height: userScale || successText ? '68vh' : '22vh',
                    backgroundSize: '100% auto',
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`
                }}
                catchMove={false}
                rounded
                onClose={() => {
                    setPopupOpen(false);
                    onClosePopup && onClosePopup();
                }}
            >
                <Popup.Close />
                <Popup.Backdrop open={popupOpen} />
                {getCon()}
            </Popup>
        </>
    );
};

export default memo(ImNpsPop);
