{"name": "@baidu/vita-pages-portrait", "version": "1.0.0", "private": true, "description": "用户档案", "main": "index.ts", "templateInfo": {"name": "default", "typescript": true, "css": "None", "framework": "React"}, "scripts": {"test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@baidu/boxx": "^4.0.4", "@baidu/health-utils": "1.1.3", "@baidu/vita-pages-im": "workspace:*", "@baidu/vita-ui-cards-common": "workspace:*", "@baidu/vita-ui-cards-wz": "workspace:*", "@baidu/vita-utils-shared": "workspace:*", "@searchfe/user-agent": "^1.9.14", "classnames": "^2.5.1", "dayjs": "^1.11.6", "jotai": "^2.12.2", "js-base64": "^3.7.7", "sse-kit": "1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"jotai-devtools": "0.12.0", "react-scan": "^0.1.3"}}