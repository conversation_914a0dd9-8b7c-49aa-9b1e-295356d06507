# Portal 组件

一个基于 React Context 的 Portal 实现，支持将组件渲染到指定的容器中。

## 功能特性

- ✅ **Provider-Consumer 模式**：通过 Context 管理 Portal 状态
- ✅ **动态挂载/卸载**：支持动态添加和移除 Portal 内容
- ✅ **订阅机制**：实时同步状态变化
- ✅ **指定容器**：支持将内容渲染到不同的容器中
- ✅ **自定义样式**：支持为不同容器设置不同的样式
- ✅ **向后兼容**：完全兼容原有 API

## 基本用法

### 1. 基础使用（向后兼容）

```tsx
import Portal from './Portal';

function App() {
    return (
        <Portal.provider>
            <div>
                {/* Portal 内容会渲染到默认 slot 中 */}
                <Portal>
                    <div>这是 Portal 内容</div>
                </Portal>
            </div>
            
            {/* 默认渲染位置 */}
            <Portal.slot />
        </Portal.provider>
    );
}
```

### 2. 指定容器使用

```tsx
import Portal from './Portal';

function App() {
    return (
        <Portal.provider>
            <div>
                {/* 渲染到默认容器 */}
                <Portal>
                    <div>默认容器内容</div>
                </Portal>
                
                {/* 渲染到指定容器 */}
                <Portal containerId="modal-container">
                    <div>模态框内容</div>
                </Portal>
                
                <Portal containerId="tooltip-container">
                    <div>提示框内容</div>
                </Portal>
            </div>
            
            {/* 不同的渲染位置 */}
            <Portal.slot /> {/* 默认容器 */}
            <Portal.slot 
                containerId="modal-container" 
                style={{zIndex: 2000}} 
            />
            <Portal.slot 
                containerId="tooltip-container" 
                style={{zIndex: 1500}} 
            />
        </Portal.provider>
    );
}
```

## API 参考

### Portal 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| children | ReactNode | - | 要渲染的内容 |
| containerId | string | undefined | 容器ID，指定渲染到哪个 slot |

### PortalSlot 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| containerId | string | undefined | 容器ID，只渲染匹配的 Portal 内容 |
| style | CSSProperties | 默认样式 | 自定义样式 |
| className | string | undefined | CSS 类名 |

### 默认样式

```css
{
    position: 'absolute',
    zIndex: 1050,
    width: '100vw',
    height: 'auto'
}
```

## 使用场景

### 1. 模态框

```tsx
<Portal containerId="modal">
    <Modal />
</Portal>

<Portal.slot 
    containerId="modal" 
    style={{
        zIndex: 2000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
    }}
/>
```

### 2. 提示框

```tsx
<Portal containerId="tooltip">
    <Tooltip />
</Portal>

<Portal.slot 
    containerId="tooltip" 
    style={{
        zIndex: 1500,
        pointerEvents: 'none'
    }}
/>
```

### 3. 通知

```tsx
<Portal containerId="notification">
    <Notification />
</Portal>

<Portal.slot 
    containerId="notification" 
    style={{
        zIndex: 1800,
        position: 'fixed',
        top: '20px',
        right: '20px'
    }}
/>
```

## 迁移指南

从旧版本迁移到新版本非常简单，因为新版本完全向后兼容：

### 旧版本代码
```tsx
<Portal.provider>
    <Portal>
        <div>内容</div>
    </Portal>
    <Portal.slot />
</Portal.provider>
```

### 新版本代码（可选升级）
```tsx
<Portal.provider>
    {/* 保持原有用法 */}
    <Portal>
        <div>默认内容</div>
    </Portal>
    
    {/* 或使用新功能 */}
    <Portal containerId="special">
        <div>特殊内容</div>
    </Portal>
    
    <Portal.slot />
    <Portal.slot containerId="special" style={{zIndex: 2000}} />
</Portal.provider>
```

## 注意事项

1. **容器ID 匹配**：Portal 的 `containerId` 必须与 PortalSlot 的 `containerId` 完全匹配
2. **Provider 包裹**：所有 Portal 和 PortalSlot 必须在 `Portal.provider` 内部
3. **样式层级**：不同容器可以设置不同的 z-index 来控制显示层级
4. **性能优化**：通过 containerId 过滤，避免不必要的重渲染
