import {createContext, useContext, useRef, useCallback, type ReactNode} from 'react';

interface PortalData {
    element: ReactNode;
    containerId?: string;
}

interface PortalContextType {
    unmount: (key: symbol) => void;
    mount: (key: symbol, element: ReactNode, containerId?: string) => void;
    getPortals: (containerId?: string) => Map<symbol, PortalData>;
    subscribe: (callback: () => void) => () => void;
}

const PortalContext = createContext<PortalContextType | null>(null);

export const PortalProvider = ({children}: {children: ReactNode}) => {
    const listeners = useRef<(() => void)[]>([]);
    const portalsRef = useRef(new Map<symbol, PortalData>());

    const mount = useCallback((key: symbol, element: ReactNode, containerId?: string) => {
        portalsRef.current.set(key, {element, containerId});
        listeners.current.forEach(listener => listener());
    }, []);

    const unmount = useCallback((key: symbol) => {
        if (portalsRef.current.has(key)) {
            portalsRef.current.delete(key);
            listeners.current.forEach(listener => listener());
        }
    }, []);

    const getPortals = useCallback((containerId?: string) => {
        const filtered = new Map<symbol, PortalData>();
        portalsRef.current.forEach((data, key) => {
            // 如果指定了 containerId，则匹配相同的 containerId
            // 如果没有指定 containerId，则匹配没有 containerId 或 containerId 为 undefined 的 Portal
            if (containerId !== undefined) {
                if (data.containerId === containerId) {
                    filtered.set(key, data);
                }
            } else {
                if (data.containerId === undefined) {
                    filtered.set(key, data);
                }
            }
        });
        return filtered;
    }, []);

    const subscribe = useCallback((callback: () => void) => {
        listeners.current.push(callback);

        return () => {
            listeners.current = listeners.current.filter(l => l !== callback);
        };
    }, []);

    return (
        <PortalContext.Provider value={{mount, unmount, getPortals, subscribe}}>
            {children}
        </PortalContext.Provider>
    );
};

export const usePortal = () => {
    const context = useContext(PortalContext);
    if (!context) {
        throw new Error('usePortal context 获取失败');
    }

    return context;
};
