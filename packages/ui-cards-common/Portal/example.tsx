import React from 'react';
import {View, Text, Button} from '@tarojs/components';
import Portal from './index';

/**
 * Portal 使用示例
 * 
 * 演示如何使用新的 containerId 功能来指定不同的渲染容器
 */
const PortalExample = () => {
    const [showDefault, setShowDefault] = React.useState(false);
    const [showModal, setShowModal] = React.useState(false);
    const [showTooltip, setShowTooltip] = React.useState(false);

    return (
        <Portal.provider>
            <View style={{padding: '20px'}}>
                <Text>Portal 功能测试</Text>
                
                {/* 控制按钮 */}
                <View style={{marginTop: '20px'}}>
                    <Button 
                        onClick={() => setShowDefault(!showDefault)}
                        style={{marginRight: '10px'}}
                    >
                        {showDefault ? '隐藏' : '显示'} 默认Portal
                    </Button>
                    
                    <Button 
                        onClick={() => setShowModal(!showModal)}
                        style={{marginRight: '10px'}}
                    >
                        {showModal ? '隐藏' : '显示'} 模态框Portal
                    </Button>
                    
                    <Button 
                        onClick={() => setShowTooltip(!showTooltip)}
                    >
                        {showTooltip ? '隐藏' : '显示'} 提示框Portal
                    </Button>
                </View>

                {/* Portal 内容 */}
                {showDefault && (
                    <Portal>
                        <View style={{
                            background: 'rgba(0, 0, 0, 0.5)',
                            color: 'white',
                            padding: '10px',
                            borderRadius: '4px'
                        }}>
                            默认Portal内容 - 渲染在默认容器中
                        </View>
                    </Portal>
                )}

                {showModal && (
                    <Portal containerId="modal-container">
                        <View style={{
                            background: 'rgba(255, 0, 0, 0.8)',
                            color: 'white',
                            padding: '20px',
                            borderRadius: '8px',
                            textAlign: 'center'
                        }}>
                            模态框内容 - 渲染在modal-container中
                        </View>
                    </Portal>
                )}

                {showTooltip && (
                    <Portal containerId="tooltip-container">
                        <View style={{
                            background: 'rgba(0, 255, 0, 0.8)',
                            color: 'white',
                            padding: '8px',
                            borderRadius: '4px',
                            fontSize: '12px'
                        }}>
                            提示框内容 - 渲染在tooltip-container中
                        </View>
                    </Portal>
                )}
            </View>

            {/* 不同的渲染容器 */}
            {/* 默认容器 */}
            <Portal.slot />
            
            {/* 模态框容器 - 更高的z-index */}
            <Portal.slot 
                containerId="modal-container" 
                style={{
                    zIndex: 2000,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            />
            
            {/* 提示框容器 - 中等z-index */}
            <Portal.slot 
                containerId="tooltip-container" 
                style={{
                    zIndex: 1500,
                    pointerEvents: 'none' // 提示框不阻止点击
                }}
            />
        </Portal.provider>
    );
};

export default PortalExample;
