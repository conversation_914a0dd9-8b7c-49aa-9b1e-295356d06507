import {type ReactNode, useEffect, useState, type CSSProperties} from 'react';
import {View} from '@tarojs/components';

import {usePortal} from './provider';

interface PortalSlotProps {
    containerId?: string;
    style?: CSSProperties;
    className?: string;
}

const PortalSlot = ({containerId, style, className}: PortalSlotProps = {}) => {
    const [conMap, setConMap] = useState(new Map<symbol, ReactNode>());
    const {getPortals, subscribe} = usePortal();

    useEffect(() => {
        const updateCon = () => {
            const newPortals = getPortals(containerId);
            setConMap(_prevMap => {
                const updatedMap = new Map<symbol, ReactNode>();

                newPortals.forEach((portalData, key) => {
                    updatedMap.set(key, portalData.element);
                });

                return updatedMap;
            });
        };

        const unsubscribe = subscribe(updateCon);
        updateCon(); // 初始化调用

        return unsubscribe;
    }, [containerId, getPortals, subscribe]);

    const defaultStyle: CSSProperties = {
        position: 'absolute',
        zIndex: 1050,
        width: '100vw',
        height: 'auto'
    };

    const mergedStyle = {...defaultStyle, ...style};

    return (
        <View style={mergedStyle} className={className}>
            {Array.from(conMap.entries()).map(([key, element]) => (
                <View key={key.description}>{element}</View>
            ))}
        </View>
    );
};

export default PortalSlot;
