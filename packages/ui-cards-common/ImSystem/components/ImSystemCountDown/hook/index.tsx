import {useState, useRef, useEffect} from 'react';
import {useDidShow, useDidHide} from '@tarojs/taro';
import {getSystemInfo} from '../../../../../pages-im/src/utils/taro/get_system_info/index';

interface IProps {
    initTime: number;
    callbackFn?: () => void;
}

const systemInfo = getSystemInfo();
const {platform} = systemInfo;

const useCountdown = (props: IProps) => {
    const {initTime, callbackFn} = props;

    const tickRef = useRef<() => void>(() => {});
    const [time, setTime] = useState(initTime || 0);
    const [timer, setTimer] = useState<ReturnType<typeof setTimeout> | null>(null);

    const tick = () => {
        if (time > 0) {
            setTime(time - 1);
        } else {
            timer && clearTimeout(timer);
            callbackFn && callbackFn();
        }
    };

    useDidShow(() => {
        console.info('系统消息触发 useDidShow，平台：', platform);
        if (platform === 'ios') {
            tickRef.current = tick;
            console.info('use-countdown useDidShow');
            tickRef.current!();

            const timerId = setInterval(() => tickRef.current!(), 1000);
            setTimer(timerId);
        }
    });

    useDidHide(() => {
        console.info('系统消息触发 useDidHide，平台：', platform);
        if (platform === 'ios') {
            console.info('use-countdown useDidHide');
            timer && clearTimeout(timer);
        }
    });

    useEffect(() => {
        tickRef.current = tick;
    });

    useEffect(() => {
        const timerId = setInterval(() => tickRef.current!(), 1000);
        setTimer(timerId);

        return () => clearInterval(timerId);
    }, [initTime]);

    return [time];
};

export default useCountdown;
