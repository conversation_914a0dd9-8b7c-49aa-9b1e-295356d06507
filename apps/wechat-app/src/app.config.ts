import {VITA_ALL_PATH} from '../path/index';

// eslint-disable-next-line no-undef
export default defineAppConfig({
    pages: VITA_ALL_PATH,
    window: {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: 'AI健康管家',
        navigationBarTextStyle: 'black'
    },
    lazyCodeLoading: 'requiredComponents',
    ...(process.env.TARO_ENV === 'weapp'
        ? {
            plugins: {
                wxacommentplugin: {
                    version: 'latest',
                    provider: 'wx82e6ae1175f264fa'
                },
                'pass-plugin': {
                    version: '1.8.8',
                    provider: 'wx5fcce12afdff9918'
                },
                'baidu-health-login-transfer': {
                    version: '1.0.17',
                    provider: 'wx8911e67a01f3eaea'
                },
                QCloudAIVoice: {
                    version: '2.3.1',
                    provider: 'wx3e17776051baf153'
                }
            }
        }
        : {})
});
