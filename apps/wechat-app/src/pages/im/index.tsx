import {Provider} from 'jotai';
import {View} from '@tarojs/components';
import {Im, utils} from '@baidu/vita-pages-im';
import {triageStreamAtomStore} from '@baidu/vita-pages-im/src/store/triageStreamAtom';
import {Portal} from '../../../../../packages/ui-cards-common';
import styles from './index.module.less';

const isIpx = utils.validateIsIphoneX();

export default function ImIndex() {
    return (
        <View className={styles.container}>
            <View className={styles.content}>
                <Provider store={triageStreamAtomStore}>
                    <Portal.provider>
                        <Im />
                        <Portal.slot />
                    </Portal.provider>
                </Provider>
            </View>
            <View style={{background: 'transparent', height: '10px'}}></View>
            {isIpx && (
                <View
                    style={{
                        background: 'transparent',
                        height: '14px'
                    }}
                />
            )}
        </View>
    );
}
