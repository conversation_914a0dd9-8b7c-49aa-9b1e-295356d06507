import {utils} from '@baidu/vita-pages-im';
import {View} from '@tarojs/components';
import {Portrait} from '@baidu/vita-pages-portrait';
import {Portal} from '../../../../../packages/ui-cards-common';
import styles from './index.module.less';

const isIpx = utils.validateIsIphoneX();

export default function PortraitIndex() {
    return (
        <View className={styles.container}>
            <View className={styles.content}>
                <Portal.provider>
                    <Portrait />
                    <Portal.slot />
                </Portal.provider>
            </View>
            <View style={{background: 'transparent', height: '10px'}}></View>
            {isIpx && (
                <View
                    style={{
                        background: '#fff',
                        height: '14px'
                    }}
                />
            )}
        </View>
    );
}
