import {Service} from '@baidu/vita-pages-common';
import {utils} from '@baidu/vita-pages-im';
import {View} from '@tarojs/components';
import styles from './index.module.less';

const isIpx = utils.validateIsIphoneX();

export default function ServiceIndex() {
    return (
        <View className={styles.container}>
            <View className={styles.content}>
                <Service />
            </View>
            <View style={{background: 'transparent', height: '10px'}}></View>
            {isIpx && (
                <View
                    style={{
                        background: '#fff',
                        height: '14px'
                    }}
                />
            )}
        </View>
    );
}
