import type {UserConfigExport} from '@tarojs/cli';

export default {
    mini: {
        optimizeMainPackage: {
            enable: true
        }
        // webpackChain(chain) {
        //     chain.optimization.minimize(true);
        //     chain
        //         .plugin('analyzer')
        //         .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, []);
        // }
    }
} satisfies UserConfigExport<'webpack5'>;
